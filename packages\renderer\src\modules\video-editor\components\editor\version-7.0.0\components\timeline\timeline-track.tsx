import { Overlay, OverlayType, TextOverlay, Track, TrackType } from '@app/rve-shared/types'
import React, { useCallback, useMemo, useEffect, useRef } from 'react'
import clsx from 'clsx'
import { useDroppable } from '@dnd-kit/core'
import { TimelineItem } from './timeline-item'
import { StyleOnlyTimelineItem } from './style-only-timeline-item.tsx'
import { GapIndicator } from './timeline-gap-indicator'
import { findLastOverlay } from '@rve/editor/utils/overlay-helper.ts'
import { getOverlayTimeRange } from '@app/rve-shared/utils'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { createOverlayFromResource, isResourceAcceptableByTrack } from '@rve/editor/utils/resource-overlay-mapper.ts'
import { useResource } from '@rve/editor/hooks/resource/useResource.tsx'
import { DEFAULT_TEXT_OVERLAY_STYLES, PIXELS_PER_FRAME } from '@rve/editor/constants'
import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-react'
import { getTrackTypeLabel, isOverlayAcceptableByTrack } from '@rve/editor/utils/track-helper.ts'
import { TimelineTrackContextMenu } from '@rve/editor/components/timeline/timeline-track-context-menu.tsx'
import { useOverlayHelper } from '@rve/editor/hooks/helpers/useOverlayHelper.ts'
import { GhostElement } from '../../types'
import { useEditorContext, useTimeline } from '@rve/editor/contexts'

export interface TimelineTrackProps extends Track {
  trackIndex: number
}

/**
 * Finds gaps between overlay items in a single timeline row
 * @param rowItems - Array of Overlay items in the current row
 * @returns Array of gap objects, each containing start and end times
 *
 * @example
 * // For a row with items: [0-30], [50-80], [100-120]
 * // Returns: [{start: 30, end: 50}, {start: 80, end: 100}]
 *
 * @description
 * This function identifies empty spaces (gaps) between overlay items in a timeline row:
 * 1. Converts each item into start and end time points
 * 2. Sorts all time points chronologically
 * 3. Identifies three types of gaps:
 *    - Gaps at the start (if first item doesn't start at 0)
 *    - Gaps between items
 *    - Gaps at the end are not considered as they're considered infinite
 */
function findGapsInRow(rowItems: Overlay[]) {
  if (rowItems.length === 0) return []

  const timePoints = rowItems
    .flatMap(item => [
      { time: item.from, type: 'start' },
      { time: item.from + item.durationInFrames, type: 'end' },
    ])
    .sort((a, b) => a.time - b.time)

  // Handle special case: if no items start at 0, add a gap from 0
  const gaps: { start: number, end: number }[] = []

  // Handle gap at the start
  if (timePoints.length > 0 && timePoints[0].time > 0) {
    gaps.push({ start: 0, end: timePoints[0].time })
  }

  // Handle gaps between items
  for (let i = 0; i < timePoints.length - 1; i++) {
    const currentPoint = timePoints[i]
    const nextPoint = timePoints[i + 1]

    if (
      currentPoint.type === 'end'
      && nextPoint.type === 'start'
      && nextPoint.time > currentPoint.time
    ) {
      gaps.push({ start: currentPoint.time, end: nextPoint.time })
    }
  }

  return gaps
}

const TrailingContainer: React.FC<TimelineTrackProps> = ({
  type, overlays, trackIndex, isGlobalTrack
}) => {
  const { isDragging, zoomScale } = useTimeline()
  const { appendOverlayToTrack } = useOverlayHelper()
  const { durationInFrames, playerDimensions, getAspectRatioDimensions } = useEditorContext()

  // 计算添加按钮的位置
  const styles = useMemo(
    () => {
      if (overlays.length === 0) {
        // 空轨道时显示在起始位置
        return {
          left: 0,
          width: durationInFrames * 1.1 * PIXELS_PER_FRAME * zoomScale
        }
      }

      // 找到最后一个 overlay 的结束位置
      const [, lastOverlayEnd] = getOverlayTimeRange(findLastOverlay(overlays))

      return {
        left: lastOverlayEnd * PIXELS_PER_FRAME * zoomScale,
        width: (durationInFrames - lastOverlayEnd) * PIXELS_PER_FRAME * zoomScale,
      }
    },
    [overlays, zoomScale, durationInFrames]
  )

  // 处理添加内容按钮点击
  const handleAddOverlay = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation()

      if (type === TrackType.STORYBOARD) {
        return appendOverlayToTrack(trackIndex, {
          type: OverlayType.STORYBOARD,
        })
      }

      if (type === TrackType.TEXT && isGlobalTrack) {
        const { width, height } = getAspectRatioDimensions()

        const overlayWidth = width / 2
        const overlayHeight = overlayWidth / 4
        const overlay: Partial<TextOverlay> = {
          content: '默认文字',
          styles: DEFAULT_TEXT_OVERLAY_STYLES,
          width: overlayWidth,
          height: overlayHeight,
          left: (width - overlayWidth) / 2,
          top: (height - overlayHeight) / 2,
        } as const

        return appendOverlayToTrack(trackIndex, {
          ...overlay,
          type: OverlayType.TEXT,
        })
      }
    },
    [type, trackIndex, overlays, playerDimensions, getAspectRatioDimensions]
  )

  return (
    <div
      className={clsx(
        'absolute inset-y-0 flex items-center px-4',
        'group', // 当悬浮在这个区域时显示
        isDragging && 'hidden', // 拖拽时完全隐藏
      )}
      style={{
        left: styles.left,
        zIndex: 40,
        width: styles.width,
      }}
    >
      {(isGlobalTrack || type === TrackType.STORYBOARD) && (
        <Button
          size="sm"
          variant="ghost"
          className="opacity-0 group-hover:opacity-100 pointer-events-auto bg-white/90 dark:bg-gray-800/90 border border-gray-200 dark:border-gray-700 shadow-sm hover:bg-white dark:hover:bg-gray-800 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-all duration-200"
          onClick={handleAddOverlay}
        >
          <Plus className="w-3 h-3 mr-1" />
          添加{getTrackTypeLabel(type)}
        </Button>
      )}
    </div>
  )
}

export const TimelineTrack: React.FC<TimelineTrackProps> = props => {
  const { type, overlays, trackIndex } = props

  const { downloadResourceToCache, getResourcePathSync } = useResource()
  const { durationInFrames, selectedOverlay, updateOverlay } = useEditorContext()
  const { appendOverlayToTrack } = useOverlayHelper()

  const {
    isDragging, draggedRowIndex, dragOverRowIndex,
    draggingOverlay, landingPoint, mousePosition, zoomScale,
    layout: { getTrackHeight }
  } = useTimeline()

  // 创建轨道元素的引用
  const trackRef = useRef<HTMLDivElement>(null)

  // 设置轨道为可放置区域
  const { setNodeRef } = useDroppable({
    id: `track-${trackIndex}`,
    data: {
      type: 'timeline-track',
      trackIndex,
      trackType: type,
    }
  })

  // 合并refs
  const combinedRef = useCallback((node: HTMLDivElement | null) => {
    setNodeRef(node)
    trackRef.current = node
  }, [setNodeRef])

  // 处理外部资源拖拽放置的逻辑
  const handleExternalResourceDrop = useCallback(async (resourceData: any, dropX: number) => {
    try {
      console.log('处理外部音频资源放置:', resourceData, '位置:', dropX)

      const { resourceType, resourceUrl, resourceId, title, durationMsec, customExt } = resourceData

      if (!resourceType || !resourceUrl) {
        console.warn('拖拽数据格式不正确:', resourceData)
        return
      }

      // 检查资源类型是否与轨道类型匹配
      if (!isResourceAcceptableByTrack(resourceType as ResourceType, type)) {
        console.warn('资源类型与轨道不匹配:', resourceType, type)
        return
      }

      // 计算放置位置（帧）
      const trackRect = trackRef.current?.getBoundingClientRect()
      if (!trackRect) return

      const dropPositionPercent = dropX / trackRect.width
      const dropFrame = Math.round(dropPositionPercent * durationInFrames)

      // 优先使用本地缓存路径（如果有）
      const localPath = getResourcePathSync(resourceType, resourceUrl)

      // 创建叠加层对象
      const resourcePath = localPath || resourceUrl
      const newOverlay = createOverlayFromResource(
        resourceType,
        resourcePath,
        { from: dropFrame, row: trackIndex },
        durationMsec,
        title,
      )

      appendOverlayToTrack(trackIndex, newOverlay, dropFrame)

      // 如果资源未缓存，异步下载
      if (!localPath) {
        downloadResourceToCache({
          url: resourceUrl,
          resourceType,
          id: resourceId,
          customExt,
        }).then(downloadedPath => {
          if (downloadedPath) {
            console.log('资源下载成功:', downloadedPath)
            updateOverlay(newOverlay.id, {
              content: downloadedPath,
            })
          } else {
            console.error('资源下载失败，但已添加到时间轴')
          }
        }).catch(error => {
          console.error('资源下载出错:', error)
        })
      }
    } catch (error) {
      console.error('放置外部资源失败:', error)
    }
  }, [type, trackIndex, durationInFrames, appendOverlayToTrack, downloadResourceToCache, getResourcePathSync, updateOverlay])

  // 监听自定义的外部资源放置事件
  useEffect(() => {
    const trackElement = trackRef.current
    if (!trackElement) return

    const handleCustomDrop = (event: CustomEvent) => {
      const { resourceData, dropX } = event.detail
      handleExternalResourceDrop(resourceData, dropX)
    }

    trackElement.addEventListener('external-resource-drop', handleCustomDrop as EventListener)

    return () => {
      trackElement.removeEventListener('external-resource-drop', handleCustomDrop as EventListener)
    }
  }, [handleExternalResourceDrop])

  const gaps = findGapsInRow(overlays)

  const renderGhostElement = (
    el: GhostElement | null, isMousePosition = false
  ) => (
    el && el.row === trackIndex && (
      <div
        className={clsx(
          'absolute inset-y-[0.9px] rounded-md border-2 bg-blue-100/30 dark:bg-gray-400/30 shadow-md border-green-500',
          (isMousePosition || el.invalid) && 'border-dashed',
          type === TrackType.NARRATION && {
            'h-[50%]': true,
            'top-[50%]': el.overlay?.type === OverlayType.SOUND,
          },
          el.invalid
            ? 'border-red-500/80'
            : isMousePosition
              ? 'dark:border-white/50 cursor-grabbing'
              : 'border-green-600',
        )}
        style={{
          left: el.from * PIXELS_PER_FRAME * zoomScale,
          width: el.durationInFrames * PIXELS_PER_FRAME * zoomScale,
          zIndex: 50,
        }}
      >
        {isMousePosition && el.overlay && (
          <StyleOnlyTimelineItem
            item={el.overlay}
            width={el.durationInFrames * PIXELS_PER_FRAME * zoomScale}
            className="w-full h-full absolute top-0 left-0 opacity-50"
          />
        )}
      </div>
    )
  )

  const renderItems = useCallback(() => {
    if (type !== TrackType.NARRATION) {
      return overlays.map(overlay => (
        <TimelineItem key={overlay.id} item={overlay} />
      ))
    }

    const textOverlays = overlays.filter(o => o.type === OverlayType.TEXT)
    const soundOverlays = overlays.filter(o => o.type === OverlayType.SOUND)

    return (
      <div className="h-full flex flex-col">
        <div className="flex-1 relative">
          {textOverlays.map(overlay => (
            <TimelineItem key={overlay.id} item={overlay} />
          ))}
        </div>
        <div className="flex-1 relative">
          {soundOverlays.map(overlay => (
            <TimelineItem key={overlay.id} item={overlay} />
          ))}
        </div>
      </div>
    )
  }, [overlays, type])

  return (
    <TimelineTrackContextMenu track={{ ...props, index: trackIndex }}>
      <div
        ref={combinedRef}
        data-track-index={trackIndex}
        style={{
          height: getTrackHeight(trackIndex)
        }}
        className={clsx(
          `bg-slate-100/90 dark:bg-gray-800 relative
         transition-all duration-200 ease-in-out
         hover:bg-slate-200/90 dark:hover:bg-gray-700/90`,
          draggingOverlay && (
            !isOverlayAcceptableByTrack(draggingOverlay, { type })
            && 'hover:cursor-not-allowed'
          ),
          {
            'border-2 border-blue-500 bg-blue-50 dark:bg-blue-900/20': dragOverRowIndex === trackIndex,
            'opacity-50': draggedRowIndex === trackIndex,
            'shadow-sm': selectedOverlay && overlays.length,
          },
        )}
      >
        {renderItems()}

        {/* Gap indicators */}
        {!isDragging && gaps.map((gap, gapIndex) => (
          <GapIndicator
            key={`gap-${trackIndex}-${gapIndex}`}
            gap={gap}
            rowIndex={trackIndex}
          />
        ))}

        {/* Ghost element with updated colors */}
        {renderGhostElement(landingPoint)}
        {renderGhostElement(mousePosition, true)}

        <TrailingContainer {...props} />
      </div>
    </TimelineTrackContextMenu>
  )
}
