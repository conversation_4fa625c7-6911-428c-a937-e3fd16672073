/**
 * TimelineGrid Component
 * Renders a grid-based timeline view for managing overlay items across multiple rows.
 * Supports drag and drop, resizing, and various item management operations.
 */

import React, { useCallback } from 'react'
import { SNAPPING_CONFIG } from '../../constants'
import { TimelineTrack } from './timeline-track'
import { useEditorContext, useTimeline } from '@rve/editor/contexts'
import {
  closestCenter,
  DndContext,
  DragEndEvent,
  DragMoveEvent,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors
} from '@dnd-kit/core'

/**
 * TimelineGrid component that displays overlay items in a row-based timeline view
 */
export const TimelineGrid: React.FC = () => {
  const { tracks, durationInFrames } = useEditorContext()

  const {
    isDragging, alignmentLines, timelineGridRef,
    layout: { totalHeight, rowGap },
    handleOverlayDragStart, handleOverlayDragMove, handleOverlayDragEnd
  } = useTimeline()

  const sensors = useSensors(useSensor(PointerSensor, {
    activationConstraint: {
      distance: 4,
    }
  }))

  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event

    // 处理时间轴内部item的拖拽
    if (active.data.current?.type === 'timeline-item') {
      const { overlay } = active.data.current as any
      return handleOverlayDragStart(overlay, 'move')
    }

    // 处理外部音频资源的拖拽 - 不需要特殊处理，让它通过即可
    if (active.data.current?.type === 'external-audio-resource') {
      // 外部资源拖拽不需要调用 handleOverlayDragStart
      // 它们会在 drop 时被 timeline-track 处理
      return
    }

    // 其他类型的拖拽被忽略
  }, [handleOverlayDragStart])

  const handleDragMove = useCallback((event: DragMoveEvent) => {
    const { active } = event

    // 只处理时间轴内部item的拖拽移动
    if (active.data.current?.type === 'timeline-item') {
      const { x: deltaX } = event.delta
      const targetTrackIndex = event.over?.data.current?.trackIndex
      return handleOverlayDragMove(deltaX, targetTrackIndex)
    }

    // 外部资源拖拽不需要特殊的移动处理
  }, [handleOverlayDragMove])

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event

    // 处理时间轴内部item的拖拽结束
    if (active.data.current?.type === 'timeline-item') {
      return handleOverlayDragEnd()
    }

    // 处理外部音频资源的拖拽结束
    if (active.data.current?.type === 'external-audio-resource' && over) {
      const targetTrackIndex = over.data.current?.trackIndex
      if (typeof targetTrackIndex === 'number') {
        // 触发轨道的资源放置处理
        const trackElement = document.querySelector(`[data-track-index="${targetTrackIndex}"]`)
        if (trackElement) {
          // 创建一个模拟的拖拽事件来触发轨道的处理逻辑
          const rect = trackElement.getBoundingClientRect()
          const dropX = (event.activatorEvent as MouseEvent).clientX - rect.left

          // 通过自定义事件通知轨道处理资源放置
          const customEvent = new CustomEvent('external-resource-drop', {
            detail: {
              resourceData: active.data.current,
              dropX,
              targetTrackIndex
            }
          })
          trackElement.dispatchEvent(customEvent)
        }
      }
    }
  }, [handleOverlayDragEnd])

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragMove={handleDragMove}
      onDragEnd={handleDragEnd}
    >
      <div
        ref={timelineGridRef}
        id="TimelineGrid"
        className="relative overflow-y-hidden h-full"
        style={{
          height: totalHeight,
          scrollbarWidth: 'none',
        }}
      >
        {/* Container for Rows and Alignment Lines */}
        <div
          className="absolute inset-0 flex flex-col py-2"
          style={{ rowGap }}
        >
          {/* Render Alignment Lines - Conditionally visible and higher contrast */}
          {isDragging
            && SNAPPING_CONFIG.enableVerticalSnapping
            && alignmentLines.map(frame => (
              <div
                key={`align-${frame}`}
                className="absolute top-0 bottom-0 w-px border-r border-dashed border-gray-500 dark:border-gray-200 z-40 pointer-events-none"
                style={{
                  left: `${(frame / durationInFrames) * 100}%`,
                  height: '100%',
                }}
                aria-hidden="true"
              />
            ))}

          {/* Render Tracks */}
          {tracks.map((track, trackIndex) => (
            <TimelineTrack
              key={trackIndex}
              trackIndex={trackIndex}
              {...track}
            />
          ))}
        </div>
      </div>
    </DndContext>
  )
}
