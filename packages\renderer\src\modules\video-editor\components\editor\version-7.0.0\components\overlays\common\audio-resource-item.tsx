import React, { ReactNode, useState, useEffect, useRef, useCallback, useMemo } from 'react'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { ResourceCacheIndicator } from '@/components/ResourceCacheIndicator'
import { InteractInfo } from '@/types/resources'
import { useResource } from '@rve/editor/hooks/resource/useResource.tsx'
import { useResourceCacheStatus } from '@rve/editor/hooks/resource/useResourceCacheStatus.tsx'
import { cn } from '@/components/lib/utils'
import { ResourceCollectionIndicator } from '@/components/ResourceCollectionIndicator'
import { ImageOff, Play, Pause } from 'lucide-react'
import { AudioProgressBar } from './audio-progress-bar'
import { useDraggable } from '@dnd-kit/core'
import { TrackType } from '@app/rve-shared/types'
import { useEditorContext } from '@rve/editor/contexts'
import { isOverlayAcceptableByTrack } from '@rve/editor/utils/track-helper.ts'
import { createOverlayFromResource } from '@rve/editor/utils/resource-overlay-mapper.ts'
import { StyleOnlyTimelineItem } from '../../timeline/style-only-timeline-item.tsx'
import { toast } from 'react-toastify'

class NativeAudioManager {

  private static instance: NativeAudioManager
  private currentAudio: HTMLAudioElement | null = null
  private currentId: string | number | null = null
  private listeners: Map<string | number, (event: 'play' | 'pause' | 'stop') => void> = new Map()

  static getInstance(): NativeAudioManager {
    if (!NativeAudioManager.instance) {
      NativeAudioManager.instance = new NativeAudioManager()
    }
    return NativeAudioManager.instance
  }

  /**
   * 注册音频实例的事件监听器
   */
  registerListener(id: string | number, callback: (event: 'play' | 'pause' | 'stop') => void) {
    this.listeners.set(id, callback)
  }

  /**
   * 移除音频实例的事件监听器
   */
  unregisterListener(id: string | number) {
    this.listeners.delete(id)
  }

  /**
   * 播放指定音频，停止其他正在播放的音频
   */
  play(audio: HTMLAudioElement, id: string | number) {
    // 如果有其他音频正在播放，先停止它
    if (this.currentAudio && this.currentId !== id) {
      this.currentAudio.pause()
      // 通知之前播放的音频实例
      const prevListener = this.listeners.get(this.currentId!)
      if (prevListener) {
        prevListener('stop')
      }
    }

    this.currentAudio = audio
    this.currentId = id

    audio.play().catch(err => {
      console.error('播放音频失败:', err)
      // 通知当前音频实例播放失败
      const listener = this.listeners.get(id)
      if (listener) {
        listener('stop')
      }
    })
  }

  /**
   * 暂停指定音频
   */
  pause(id: string | number) {
    if (this.currentId === id && this.currentAudio) {
      this.currentAudio.pause()
    }
  }

  /**
   * 停止指定音频
   */
  stop(id: string | number) {
    if (this.currentId === id && this.currentAudio) {
      this.currentAudio.pause()
      this.currentAudio.currentTime = 0
      this.currentAudio = null
      this.currentId = null
    }
  }

  /**
   * 检查指定音频是否正在播放
   */
  isPlaying(id: string | number): boolean {
    return this.currentId === id && !!this.currentAudio && !this.currentAudio.paused
  }
}

export interface AudioResourceItemProps {
  /**
   * 资源ID
   */
  id: string | number
  /**
   * 资源标题
   */
  title: string
  /**
   * 资源描述或副标题
   */
  description?: string | number
  /**
   * 资源缩略图URL
   */
  thumbnailUrl?: string
  /**
   * 默认图标 (Lucide图标)
   */
  icon?: ReactNode
  /**
   * 音频URL
   */
  audioUrl?: string
  /**
   * 是否正在加载
   */
  isLoading?: boolean
  /**
   * 点击添加按钮的回调
   */
  onAdd?: () => void
  /**
   * 点击整个资源项的回调
   */
  onClick?: () => void

  /**
   * 自定义内容
   */
  children?: ReactNode
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 资源类型，用于检查本地缓存
   */
  resourceType?: ResourceType
  /**
   * 资源URL，用于检查本地缓存
   */
  resourceUrl?: string
  /**
   * 资源时长（毫秒）
   */
  durationMsec?: number
  /**
   * 自定义扩展名
   */
  customExt?: string,

  /**
   * 是否显示收藏按钮
   */
  showCollectionButton?: boolean
  /**
   * 交互信息，包含收藏状态
   */
  interactInfo?: InteractInfo
  /**
   * 收藏状态变更回调
   */
  onCollectionChange?: (collected: boolean) => void
  version?: string
}

/**
 * 音频资源项组件
 * 重构后使用原生 HTML5 音频和交互式进度条
 */
export function AudioResourceItem({
  id,
  title,
  thumbnailUrl,
  icon,
  audioUrl,
  isLoading = false,
  onAdd,
  onClick,
  children,
  className = '',
  resourceType,
  resourceUrl,
  description,
  durationMsec,
  showCollectionButton = true,
  interactInfo,
  onCollectionChange,
  customExt,
  version,
}: AudioResourceItemProps) {
  // 音频播放状态
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0) // 当前播放时间（秒）
  const [duration, setDuration] = useState(0) // 音频总时长（秒）
  const [audioInitialized, setAudioInitialized] = useState(false)
  const [imageLoadError, setImageLoadError] = useState(false)
  const [showProgressBar, setShowProgressBar] = useState(false) // 控制进度条显示

  const audioRef = useRef<HTMLAudioElement | null>(null)
  const audioManager = NativeAudioManager.getInstance()

  const isCollected = interactInfo?.collected || false

  // 使用缓存状态查询 Hook，与 ResourceItem 保持一致
  const { isCached, isChecking } = useResourceCacheStatus(resourceType, resourceUrl)
  const { getResourcePathSync } = useResource()

  // 当缩略图 URL 改变时重置图片加载错误状态
  useEffect(() => {
    setImageLoadError(false)
  }, [thumbnailUrl])

  // 音频事件处理函数
  const handleTimeUpdate = useCallback(() => {
    if (audioRef.current && audioRef.current.duration) {
      setCurrentTime(audioRef.current.currentTime)
    }
  }, [])

  const handleLoadedMetadata = useCallback(() => {
    if (audioRef.current && audioRef.current.duration) {
      setDuration(audioRef.current.duration)
    }
  }, [])

  const handleAudioEnded = useCallback(() => {
    setIsPlaying(false)
    setCurrentTime(0)
    setShowProgressBar(false)
    audioManager.stop(id)
  }, [id])

  const handleAudioError = useCallback(() => {
    setIsPlaying(false)
    setShowProgressBar(false)
    console.error('音频播放错误')
  }, [])

  // 音频管理器事件监听器
  const handleAudioManagerEvent = useCallback((event: 'play' | 'pause' | 'stop') => {
    switch (event) {
      case 'stop':
        setIsPlaying(false)
        setShowProgressBar(false)
        if (audioRef.current) {
          audioRef.current.currentTime = 0
        }
        setCurrentTime(0)
        break
      case 'pause':
        setIsPlaying(false)
        break
      case 'play':
        setIsPlaying(true)
        setShowProgressBar(true)
        break
    }
  }, [])

  const collectionIndicator = resourceType && id && showCollectionButton && (
    <div className={cn(
      'absolute right-0 top-0 transition-opacity duration-200',
      isCollected ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
    )}
    >
      <ResourceCollectionIndicator
        resourceType={resourceType}
        resourceId={id}
        isCollected={isCollected}
        size={12}
        onCollectionChange={onCollectionChange}
      />
    </div>
  )

  // 初始化音频元素
  const initializeAudio = useCallback(() => {
    if (audioInitialized || !audioUrl) return null

    const audio = new Audio()
    audio.preload = 'metadata'
    audioRef.current = audio

    // 添加音频事件监听器
    audio.addEventListener('timeupdate', handleTimeUpdate)
    audio.addEventListener('loadedmetadata', handleLoadedMetadata)
    audio.addEventListener('ended', handleAudioEnded)
    audio.addEventListener('error', handleAudioError)

    // 设置音频源
    if (isCached && resourceType && resourceUrl) {
      const localPath = getResourcePathSync(resourceType, resourceUrl)
      if (localPath) {
        audio.src = localPath
      } else {
        audio.src = audioUrl
      }
    } else {
      audio.src = audioUrl
    }

    // 注册到音频管理器
    audioManager.registerListener(id, handleAudioManagerEvent)
    setAudioInitialized(true)

    return audio
  }, [audioUrl, handleTimeUpdate, handleLoadedMetadata, handleAudioEnded, handleAudioError, handleAudioManagerEvent, isCached, resourceType, resourceUrl, getResourcePathSync, audioInitialized, id])

  // 播放/暂停切换
  const togglePlay = useCallback((e: React.MouseEvent) => {
    e.stopPropagation()

    // 如果音频尚未初始化，先初始化
    if (!audioInitialized) {
      const audio = initializeAudio()
      if (!audio) return
    }

    if (!audioRef.current) return

    if (isPlaying) {
      audioManager.pause(id)
      setIsPlaying(false)
      setShowProgressBar(false)
    } else {
      audioManager.play(audioRef.current, id)
      setIsPlaying(true)
      setShowProgressBar(true)
    }
  }, [audioInitialized, initializeAudio, isPlaying, id])

  // AudioProgressBar 寻轨处理
  const handleSeek = useCallback((time: number) => {
    if (audioRef.current) {
      audioRef.current.currentTime = time
      setCurrentTime(time)
    }
  }, [])

  // 获取时间轴相关的hooks
  const { tracks } = useEditorContext()

  // 计算拖拽预览的样式和内容
  const dragPreviewOverlay = useMemo(() => {
    if (!resourceType || !resourceUrl) return null

    try {
      const audioDurationMs = durationMsec || (audioRef.current?.duration ? Math.round(audioRef.current.duration * 1000) : 10000)

      return createOverlayFromResource(
        resourceType,
        resourceUrl,
        { from: 0, row: 0 },
        audioDurationMs,
        title
      )
    } catch (error) {
      console.error('创建拖拽预览overlay失败:', error)
      toast('无法创建音频预览', { type: 'error' })
      return null
    }
  }, [resourceType, resourceUrl, durationMsec, title])

  // 检查是否可以拖拽到指定轨道类型
  const canDropToTrackType = useCallback((trackType: TrackType) => {
    if (!dragPreviewOverlay) return false
    return isOverlayAcceptableByTrack(dragPreviewOverlay, { type: trackType })
  }, [dragPreviewOverlay])

  // 验证拖拽数据的有效性
  const validateDragData = useCallback(() => {
    if (!resourceType || !resourceUrl) {
      toast('缺少必要的资源信息', { type: 'error' })
      return false
    }

    if (!dragPreviewOverlay) {
      toast('无法创建音频预览', { type: 'error' })
      return false
    }

    // 检查是否有兼容的轨道
    const hasCompatibleTrack = tracks.some(track => canDropToTrackType(track.type))
    if (!hasCompatibleTrack) {
      toast('当前项目中没有兼容的音频轨道', { type: 'warning' })
      return false
    }

    return true
  }, [resourceType, resourceUrl, dragPreviewOverlay, tracks, canDropToTrackType])

  const { setNodeRef, listeners, attributes, isDragging: isItemDragging } = useDraggable({
    id: `audio-resource-${id}`,
    data: {
      type: 'audio-resource',
      resourceType,
      resourceUrl,
      resourceId: id,
      title,
      durationMsec: durationMsec || (audioRef.current?.duration ? Math.round(audioRef.current.duration * 1000) : 10000),
      customExt,
      overlay: dragPreviewOverlay, // 传递预览overlay用于拖拽处理
      canDropToTrackType, // 传递验证函数
    },
    disabled: !resourceType || !resourceUrl || !dragPreviewOverlay || !validateDragData()
  })

  // 添加原生 HTML5 拖拽支持，用于与 timeline-track 的兼容性
  const handleDragStart = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    if (!resourceType || !resourceUrl) {
      console.warn('拖拽开始失败: 缺少必要的资源信息', { resourceType, resourceUrl })
      return
    }

    // 设置拖拽数据，与原有的格式保持兼容
    const dragData = {
      resourceType,
      resourceUrl,
      id: String(id), // 确保id是字符串
      title: title || '', // 确保title不为undefined
      durationMsec: durationMsec || (audioRef.current?.duration ? Math.round(audioRef.current.duration * 1000) : 10000),
      customExt: customExt || undefined, // 明确处理undefined
    }

    try {
      // 验证数据的有效性
      if (!dragData.resourceType || !dragData.resourceUrl) {
        throw new Error('缺少必要的资源信息')
      }

      const jsonString = JSON.stringify(dragData)
      console.log('设置拖拽数据:', dragData, '序列化后:', jsonString)

      // 验证序列化后的字符串
      if (!jsonString || jsonString === '{}') {
        throw new Error('序列化后的数据为空')
      }

      e.dataTransfer.setData('application/json', jsonString)
      e.dataTransfer.effectAllowed = 'copy'

      if (thumbnailUrl) {
        const img = new Image()
        img.src = thumbnailUrl
        e.dataTransfer.setDragImage(img, 25, 25)
      }
    } catch (error) {
      console.error('设置拖拽数据失败:', error, '原始数据:', dragData)
      toast('拖拽设置失败', { type: 'error' })
    }
  }, [resourceType, resourceUrl, id, title, durationMsec, thumbnailUrl, customExt])

  const cacheIndicator = resourceType && resourceUrl && (
    <div className={cn(
      'absolute right-0 bottom-0 transition-opacity duration-200',
      isLoading ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
    )}
    >
      <ResourceCacheIndicator
        resourceType={resourceType}
        resourceUrl={resourceUrl}
        isLoading={isLoading}
        size={12}
        onDownload={onAdd}
        version={version}
        isCached={isCached}
        isChecking={isChecking}
      />
    </div>
  )
  const handleClick = useCallback((e: React.MouseEvent) => {
    if (onClick) {
      onClick()
    } else if (audioUrl) {
      togglePlay(e)
    }
  }, [onClick, audioUrl, togglePlay])

  // 组件清理
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        // 停止播放
        audioRef.current.pause()
        audioRef.current.src = ''
        audioRef.current.load()

        // 移除事件监听器
        audioRef.current.removeEventListener('timeupdate', handleTimeUpdate)
        audioRef.current.removeEventListener('loadedmetadata', handleLoadedMetadata)
        audioRef.current.removeEventListener('ended', handleAudioEnded)
        audioRef.current.removeEventListener('error', handleAudioError)

        // 从音频管理器中注销
        audioManager.unregisterListener(id)
        audioManager.stop(id)
      }
    }
  }, [handleTimeUpdate, handleLoadedMetadata, handleAudioEnded, handleAudioError, id])

  // 拖拽预览组件 - 显示与时间轴音频item一致的样式
  const DragPreview = () => {
    if (!dragPreviewOverlay || !isItemDragging) return null

    return (
      <div
        className="fixed pointer-events-none z-[9999]"
        style={{
          transform: 'translate(-50%, -50%)',
          width: '200px',
          height: '32px',
          top: '50%',
          left: '50%'
        }}
      >
        <StyleOnlyTimelineItem
          item={dragPreviewOverlay}
          width={200}
          height={32}
          className="opacity-90 shadow-2xl border-2 border-white/20"
        >
          {/* 添加音频波形图标或标识 */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-white text-xs font-medium truncate px-2">
              {title || '音频'}
            </div>
          </div>
        </StyleOnlyTimelineItem>
      </div>
    )
  }

  return (
    <>
      <div className={`aspect-square ${className}`}>
        <div
          ref={setNodeRef}
          className={cn(
            `group relative w-full h-full
            bg-muted/30
            rounded dark:bg-gray-800/40
            border dark:border-gray-700/10
            hover:border-blue-500/20 dark:hover:border-blue-500/20
            hover:bg-blue-500/5 dark:hover:bg-blue-500/5
            transition-all overflow-hidden`,
            isCached ? 'border-green-500/20 dark:border-green-500/20' : '',
            resourceType && resourceUrl ? 'cursor-grab active:cursor-grabbing' : '',
            isItemDragging && 'opacity-50'
          )}
          onClick={handleClick}
          draggable={!!resourceType && !!resourceUrl}
          onDragStart={handleDragStart}
          {...listeners}
          {...attributes}
        >
          {description && (
            <div className="text-[10px] border border-neutral-800 bg-neutral-900/80 rounded px-1 h-4 flex items-center justify-center text-muted absolute bottom-2 left-2 z-10">
              {description}
            </div>
          )}

          {thumbnailUrl ? (
            <div className="absolute inset-0 flex items-center justify-center">
              {imageLoadError ? (
                <div className="flex items-center justify-center text-gray-400">
                  <ImageOff className="w-8 h-8" />
                </div>
              ) : (
                <img
                  src={thumbnailUrl}
                  alt={title}
                  className="max-w-full max-h-full object-contain"
                  loading="lazy"
                  onError={() => setImageLoadError(true)}
                  onLoad={() => setImageLoadError(false)}
                />
              )}
            </div>
          ) : icon && (
            <div className="absolute inset-0 flex items-center justify-center text-gray-400">
              {icon}
            </div>
          )}

          {/* 播放/暂停按钮 - 悬浮显示，播放时默认显示 */}
          {audioUrl && (
            <button
              onClick={togglePlay}
              className={cn(
                'absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2',
                'rounded-full p-3 shadow-lg',
                'bg-black/70 hover:bg-black/90 text-white',
                'transition-all duration-200 z-30',
                'opacity-0 group-hover:opacity-100',
                isPlaying && 'opacity-100'
              )}
            >
              {isPlaying ? <Pause size={20} /> : <Play size={20} />}
            </button>
          )}

          {/* 竖直进度指示器 - 跟随播放进度从左到右移动 */}
          {(showProgressBar || isPlaying) && audioUrl && duration > 0 && (
            <div
              className="absolute top-0 bottom-0 w-0.5 bg-blue-500 z-25 transition-all duration-100"
              style={{
                left: `${duration > 0 ? (currentTime / duration) * 100 : 0}%`,
                boxShadow: '0 0 4px rgba(59, 130, 246, 0.5)'
              }}
            />
          )}

          {/* 使用封装好的 AudioProgressBar 组件 */}
          {(showProgressBar || isPlaying) && audioUrl && duration > 0 && (
            <div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/60 to-transparent z-20">
              <AudioProgressBar
                currentTime={currentTime}
                duration={duration}
                isPlaying={isPlaying}
                onSeek={handleSeek}
                height={4}
                className="w-full"
                showTimeLabels={false}
              />
            </div>
          )}

          {/* 缓存和收藏指示器 */}
          {cacheIndicator}
          {collectionIndicator}
        </div>

        {/* 子内容 */}
        {children}

        {/* 标题 */}
        {title && (
          <div className="text-xs w-full text-neutral-300 truncate mt-2">
            {title}
          </div>
        )}
      </div>

      {/* 拖拽预览 */}
      <DragPreview />
    </>
  )
}
